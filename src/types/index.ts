import type { User as FirebaseUser } from "firebase/auth";

export interface UserProfile extends FirebaseUser {
  tubeCoins?: number;
}

export interface Campaign {
  id: string;
  creatorId: string;
  videoUrl: string;
  youtubeVideoId?: string;
  title: string;
  description?: string;
  tags?: string[];
  coinsPerView: number;
  totalBudget: number;
  remainingBudget: number;
  views: number;
  isActive: boolean;
  createdAt: Date;
  thumbnailUrl?: string;
}

export interface Video {
  id: string;
  userId: string;
  youtubeUrl: string;
  title: string;
  description?: string;
  tags?: string[];
  // any other relevant video metadata
}

export interface Offer {
  id: string;
  title: string;
  description: string;
  rewardAmount: number; // TubeCoins
  provider: string; // e.g., "OfferToro", "AdGem"
  imageUrl?: string;
  actionUrl: string;
}

export interface LeaderboardEntry {
  userId: string;
  displayName: string;
  photoURL?: string;
  score: number; // Could be TubeCoins earned for viewers, or TubeCoins spent/views generated for influencers
}
