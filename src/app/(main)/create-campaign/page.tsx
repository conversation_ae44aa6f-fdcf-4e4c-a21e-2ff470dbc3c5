
"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";
import type { Campaign } from "@/types";
import { zodResolver } from "@hookform/resolvers/zod";
import { Coins, Link2, ListChecks, Loader2, Text, Type } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { suggestVideoMetadata, type SuggestVideoMetadataInput, type SuggestVideoMetadataOutput } from '@/ai/flows/suggest-video-metadata';


const campaignFormSchema = z.object({
  videoUrl: z.string().url({ message: "Please enter a valid YouTube URL." }),
  title: z.string().min(5, { message: "Title must be at least 5 characters." }).max(100),
  description: z.string().min(10, { message: "Description must be at least 10 characters." }).max(5000).optional(),
  tags: z.string().optional(), // Comma-separated tags
  coinsPerView: z.coerce.number().min(1, { message: "Coins per view must be at least 1." }),
  totalBudget: z.coerce.number().min(10, { message: "Total budget must be at least 10 coins." }),
});

type CampaignFormData = z.infer<typeof campaignFormSchema>;

export default function CreateCampaignPage() {
  const { user, tubeCoins, setTubeCoins, fetchTubeCoins } = useAuth();
  const { toast } = useToast();
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isAISuggesting, setIsAISuggesting] = useState(false);

  const form = useForm<CampaignFormData>({
    resolver: zodResolver(campaignFormSchema),
    defaultValues: {
      videoUrl: "",
      title: "",
      description: "",
      tags: "",
      coinsPerView: 1,
      totalBudget: 100,
    },
  });
  
  const currentBudget = form.watch("totalBudget");

  const handleAISuggestions = async () => {
    const videoDesc = form.getValues("description") || "";
    const title = form.getValues("title") || "Video title"; // Use title as a keyword source
    
    if (!videoDesc.trim()) {
      toast({
        title: "AI Suggestions",
        description: "Please provide a video description or title for AI suggestions.",
        variant: "destructive",
      });
      return;
    }

    setIsAISuggesting(true);
    try {
      const input: SuggestVideoMetadataInput = {
        videoContentDescription: videoDesc,
        targetAudience: "General YouTube audience", // Or get this from another field
        keywords: title, // Using title as keywords for simplicity
      };
      const suggestions: SuggestVideoMetadataOutput = await suggestVideoMetadata(input);
      
      if (suggestions.suggestedDescription && !form.getValues("description")) { // Only set if description is empty
        form.setValue("description", suggestions.suggestedDescription);
      }
      if (suggestions.suggestedTags && suggestions.suggestedTags.length > 0) {
         form.setValue("tags", suggestions.suggestedTags.join(", "));
      }
      toast({
        title: "AI Suggestions Applied",
        description: "Description and tags have been updated with AI suggestions.",
      });
    } catch (error) {
      console.error("AI Suggestion Error:", error);
      toast({
        title: "AI Suggestion Failed",
        description: "Could not generate AI suggestions at this time.",
        variant: "destructive",
      });
    } finally {
      setIsAISuggesting(false);
    }
  };


  const onSubmit = async (data: CampaignFormData) => {
    if (!user) {
      toast({ title: "Error", description: "You must be logged in to create a campaign.", variant: "destructive" });
      return;
    }
    if ((tubeCoins || 0) < data.totalBudget) {
      toast({ title: "Insufficient Funds", description: "You don't have enough TubeCoins for this budget.", variant: "destructive" });
      return;
    }

    setIsSubmitting(true);

    // Simulate campaign creation
    const newCampaign: Partial<Campaign> = {
      creatorId: user.uid,
      videoUrl: data.videoUrl,
      title: data.title,
      description: data.description,
      tags: data.tags?.split(",").map(tag => tag.trim()).filter(tag => tag),
      coinsPerView: data.coinsPerView,
      totalBudget: data.totalBudget,
      remainingBudget: data.totalBudget,
      views: 0,
      isActive: true,
      createdAt: new Date(),
    };

    // In a real app, save to Firestore: await addDoc(collection(db, "campaigns"), newCampaign);
    // And update user's tubeCoins: await updateDoc(doc(db, "users", user.uid), { tubeCoins: newTubeCoins });
    console.log("New campaign data:", newCampaign);
    
    // Simulate delay and update local state
    await new Promise(resolve => setTimeout(resolve, 1000));
    const newTubeCoins = (tubeCoins || 0) - data.totalBudget;
    setTubeCoins(newTubeCoins); // Update context state
    await fetchTubeCoins(); // Sync with "backend"

    toast({
      title: "Campaign Created!",
      description: `Your campaign "${data.title}" is now active.`,
    });
    setIsSubmitting(false);
    router.push("/dashboard"); // Or to a "My Campaigns" page
  };

  if (!user) return null; // AuthProvider handles loading state

  return (
    <div className="container mx-auto py-8 px-4 md:px-6 max-w-3xl">
      <Card className="shadow-xl">
        <CardHeader>
          <CardTitle className="text-3xl font-bold flex items-center gap-2">
            <ListChecks className="h-8 w-8 text-primary" /> Create New Campaign
          </CardTitle>
          <CardDescription>
            Promote your YouTube video and get more views by offering TubeCoins.
            You currently have <strong className="text-primary">{tubeCoins?.toLocaleString() || 0}</strong> TubeCoins.
          </CardDescription>
        </CardHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <CardContent className="space-y-6">
              <FormField
                control={form.control}
                name="videoUrl"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-base">YouTube Video URL</FormLabel>
                    <div className="flex items-center gap-2">
                      <Link2 className="h-5 w-5 text-muted-foreground" />
                      <FormControl>
                        <Input placeholder="https://www.youtube.com/watch?v=your_video_id" {...field} />
                      </FormControl>
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-base">Video Title</FormLabel>
                     <div className="flex items-center gap-2">
                      <Type className="h-5 w-5 text-muted-foreground" />
                      <FormControl>
                        <Input placeholder="Enter a catchy title for your video" {...field} />
                      </FormControl>
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-base">Video Description</FormLabel>
                    <div className="flex items-start gap-2">
                      <Text className="h-5 w-5 text-muted-foreground mt-2.5" />
                      <FormControl>
                        <Textarea
                          placeholder="Describe your video content (min. 10 characters). This helps AI suggestions."
                          className="min-h-[100px]"
                          {...field}
                        />
                      </FormControl>
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="tags"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-base">Tags (comma-separated)</FormLabel>
                     <div className="flex items-center gap-2">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-tags text-muted-foreground"><path d="M9 5H2v7l6.29 6.29c.94.94 2.48.94 3.42 0l3.58-3.58c.94-.94.94-2.48 0-3.42L9 5Z"/><path d="M6 9.01V9"/><path d="m15 5 6.3 6.3a2.69 2.69 0 0 1 0 3.79L17.5 19a2.69 2.69 0 0 1-3.79 0L10 15.21"/></svg>
                        <FormControl>
                           <Input placeholder="e.g., tutorial, gaming, vlog" {...field} />
                        </FormControl>
                    </div>
                    <FormDescription>Relevant tags help users find your video.</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <Button type="button" variant="outline" onClick={handleAISuggestions} disabled={isAISuggesting || isSubmitting} className="w-full sm:w-auto">
                {isAISuggesting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Lightbulb className="mr-2 h-4 w-4" /> }
                Get AI Suggestions for Description & Tags
              </Button>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="coinsPerView"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-base">TubeCoins per View</FormLabel>
                      <div className="flex items-center gap-2">
                        <Coins className="h-5 w-5 text-yellow-500" />
                        <FormControl>
                          <Input type="number" placeholder="e.g., 10" {...field} />
                        </FormControl>
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="totalBudget"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-base">Total Campaign Budget (TubeCoins)</FormLabel>
                       <div className="flex items-center gap-2">
                        <Coins className="h-5 w-5 text-yellow-500" />
                        <FormControl>
                          <Input type="number" placeholder="e.g., 1000" {...field} />
                        </FormControl>
                      </div>
                      <FormDescription>
                        Max TubeCoins: {tubeCoins?.toLocaleString() || 0}.
                        { currentBudget > (tubeCoins || 0) && <span className="text-destructive"> Budget exceeds balance.</span>}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
            <CardFooter className="border-t pt-6">
              <Button type="submit" className="w-full md:w-auto" disabled={isSubmitting || currentBudget > (tubeCoins || 0)}>
                {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Create Campaign
              </Button>
            </CardFooter>
          </form>
        </Form>
      </Card>
    </div>
  );
}
