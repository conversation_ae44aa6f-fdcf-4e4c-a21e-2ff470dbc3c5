
"use client";

import { useState, useEffect, useRef, useMemo, useCallback } from "react";
import { useRouter } from "next/navigation";
import type { Campaign, UserProfile } from "@/types";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Play, Pause, Coins, Eye, Clock, TrendingUp, Upload, Gift, History, CreditCard, ShoppingBag, Users, User } from "lucide-react";
import Image from "next/image";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";
import { Progress } from "@/components/ui/progress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { StatsCards } from "@/components/dashboard/stats-cards";

// Declaración de tipos para la API de YouTube
declare global {
  interface Window {
    YT: any;
    onYouTubeIframeAPIReady: () => void;
  }
}

interface YouTubePlayer {
  playVideo: () => void;
  pauseVideo: () => void;
  stopVideo: () => void;
  getCurrentTime: () => number;
  getDuration: () => number;
  getPlayerState: () => number;
  destroy: () => void;
}

// Mock function to simulate fetching campaigns
const fetchCampaigns = async (): Promise<Campaign[]> => {
  await new Promise(resolve => setTimeout(resolve, 500)); 
  const mockCampaigns: Campaign[] = [
    {
      id: "campaign1",
      creatorId: "creatorA",
      videoUrl: "https://www.youtube.com/watch?v=dQw4w9WgXcQ", 
      youtubeVideoId: "dQw4w9WgXcQ",
      title: "Learn Next.js in 10 Minutes",
      coinsPerView: 10,
      totalBudget: 1000,
      remainingBudget: 500,
      views: 50,
      isActive: true,
      createdAt: new Date(),
      thumbnailUrl: "https://placehold.co/600x400.png?text=Next.js+Tutorial",
      description: "A quick tutorial on Next.js fundamentals."
    },
    {
      id: "campaign2",
      creatorId: "creatorB",
      videoUrl: "https://www.youtube.com/watch?v=ysz5S6PUM-U",
      youtubeVideoId: "ysz5S6PUM-U",
      title: "Ultimate Travel Vlog: Bali",
      coinsPerView: 15,
      totalBudget: 1500,
      remainingBudget: 300,
      views: 80,
      isActive: true,
      createdAt: new Date(),
      thumbnailUrl: "https://placehold.co/600x400.png?text=Bali+Vlog",
      description: "Explore the beautiful island of Bali with me!"
    },
    {
      id: "campaign3",
      creatorId: "creatorC",
      videoUrl: "https://www.youtube.com/watch?v=3JZ_D3ELwOQ",
      youtubeVideoId: "3JZ_D3ELwOQ",
      title: "Cooking Masterclass: Pasta",
      coinsPerView: 5,
      totalBudget: 500,
      remainingBudget: 450,
      views: 10,
      isActive: true,
      createdAt: new Date(),
      thumbnailUrl: "https://placehold.co/600x400.png?text=Pasta+Cooking",
      description: "Learn to cook authentic Italian pasta."
    },
  ];
  return mockCampaigns.filter(c => c.isActive && c.remainingBudget > 0);
};

const REQUIRED_WATCH_SECONDS = 30; 

export default function DashboardPage() {
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [selectedCampaign, setSelectedCampaign] = useState<Campaign | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isWatching, setIsWatching] = useState(false);
  const [watchProgress, setWatchProgress] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);
  const [youtubePlayer, setYoutubePlayer] = useState<YouTubePlayer | null>(null);
  // Estado para el ID del video de YouTube
  const [youtubeVideoId, setYoutubeVideoId] = useState<string | null>(null);
  const [isYouTubeIframeAPILoaded, setIsYouTubeIframeAPILoaded] = useState(false);
  const [isYouTubeIframeAPIReady, setIsYouTubeIframeAPIReady] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState("overview");
  const [videoDuration, setVideoDuration] = useState(0);
  const playerRef = useRef<HTMLDivElement>(null);
  const progressIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const { user } = useAuth();
  const { toast } = useToast();
  const router = useRouter();

  // Función para formatear el tiempo en minutos:segundos
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Función para extraer el ID de un video de YouTube desde una URL
  const getYouTubeVideoId = useCallback((url: string): string | null => {
    if (!url) return null;
    try {
      // Manejar URLs de YouTube en diferentes formatos
      const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
      const match = url.match(regExp);
      return (match && match[2].length === 11) ? match[2] : null;
    } catch (e) {
      console.error('Error extracting YouTube video ID:', e);
      return null;
    }
  }, []);
  
  // Función para generar la URL de incrustación de YouTube
  const getYouTubeEmbedUrl = useCallback((videoUrl: string) => {
    try {
      const videoId = getYouTubeVideoId(videoUrl);
      if (!videoId) return null;
      
      return {
        videoId,
        embedUrl: `https://www.youtube.com/embed/${videoId}?enablejsapi=1&origin=${window.location.origin}`,
      };
    } catch (e) {
      console.error('Error generating YouTube embed URL:', e);
      return null;
    }
  }, [getYouTubeVideoId]);

  // Obtener datos del video de YouTube
  const youtubeVideoData = useMemo(() => {
    if (!selectedCampaign) return null;
    return getYouTubeEmbedUrl(selectedCampaign.videoUrl);
  }, [selectedCampaign, getYouTubeEmbedUrl]);

  // Obtener el ID del video de YouTube de la campaña seleccionada
  const currentYoutubeVideoId = useMemo(() => {
    if (!selectedCampaign) return null;
    return getYouTubeVideoId(selectedCampaign.videoUrl);
  }, [selectedCampaign, getYouTubeVideoId]);

  // Cargar campañas al montar el componente
  useEffect(() => {
    const loadCampaigns = async () => {
      try {
        setIsLoading(true);
        const data = await fetchCampaigns();
        setCampaigns(data);
        setIsLoading(false);
      } catch (error) {
        console.error("Error cargando campañas:", error);
        setIsLoading(false);
      }
    };

    loadCampaigns();
  }, []);

  // Efecto para manejar el progreso del tiempo de visualización
  // Efecto para manejar el progreso del video
  useEffect(() => {
    if (!isWatching || !selectedCampaign || watchProgress >= 100 || !isVideoPlaying || !youtubePlayer) {
      return;
    }
    
    const timer = setInterval(() => {
      try {
        // Obtener el tiempo actual del video desde el reproductor
        const currentTime = youtubePlayer.getCurrentTime();
        const duration = youtubePlayer.getDuration();
        
        if (duration > 0) {
          const newProgress = Math.min((currentTime / REQUIRED_WATCH_SECONDS) * 100, 100);
          
          // Actualizar el tiempo actual
          setCurrentTime(Math.min(currentTime, REQUIRED_WATCH_SECONDS));
          
          // Actualizar el progreso
          if (newProgress > watchProgress) {
            setWatchProgress(newProgress);
          }
          
          // Detener el temporizador si alcanzamos el 100%
          if (newProgress >= 100) {
            clearInterval(timer);
          }
        }
      } catch (error) {
        console.error('Error updating video progress:', error);
        clearInterval(timer);
      }
    }, 500);
    
    return () => clearInterval(timer);
  }, [isWatching, selectedCampaign, watchProgress, isVideoPlaying, youtubePlayer]);

  // Efecto para actualizar el progreso del video cuando está reproduciéndose
  useEffect(() => {
    if (!youtubePlayer) return;

    let timer: NodeJS.Timeout;
    
    const updateProgress = () => {
      try {
        const time = youtubePlayer.getCurrentTime();
        const duration = youtubePlayer.getDuration();
        
        if (duration > 0) {
          setCurrentTime(time);
          setVideoDuration(duration);
          
          const progress = (time / duration) * 100;
          setWatchProgress(prevProgress => {
            const newProgress = Math.min(100, Math.max(0, progress));
            return newProgress > prevProgress ? newProgress : prevProgress;
          });
        }
      } catch (error) {
        console.error('Error updating video progress:', error);
      }
    };

    // Iniciar el temporizador si el video ya está reproduciéndose
    if (isVideoPlaying) {
      updateProgress();
      timer = setInterval(updateProgress, 1000);
    }

    // Limpieza
    return () => {
      clearInterval(timer);
    };
  }, [youtubePlayer, isVideoPlaying]);

  // Estado para rastrear si la API de YouTube está lista
  const [isYouTubeAPILoaded, setIsYouTubeAPILoaded] = useState(false);
  const [isYouTubePlayerReady, setIsYouTubePlayerReady] = useState(false);
  
  // Efecto para cargar la API de YouTube
  useEffect(() => {
    // Verificar si ya está cargada la API
    if (window.YT) {
      setIsYouTubeAPILoaded(true);
      return;
    }

    // Configurar la función global de inicialización
    window.onYouTubeIframeAPIReady = () => {
      console.log('YouTube API ready');
      setIsYouTubeAPILoaded(true);
    };

    // Crear el script de la API de YouTube
    const tag = document.createElement('script');
    tag.src = 'https://www.youtube.com/iframe_api';
    tag.async = true;
    
    // Insertar el script en el documento
    const firstScriptTag = document.getElementsByTagName('script')[0];
    if (firstScriptTag?.parentNode) {
      firstScriptTag.parentNode.insertBefore(tag, firstScriptTag);
    }
    
    // Limpiar al desmontar
    return () => {
      if (tag.parentNode) {
        tag.parentNode.removeChild(tag);
      }
      // Restaurar la función original o eliminar la referencia
      if (window.onYouTubeIframeAPIReady) {
        window.onYouTubeIframeAPIReady = () => {}; // Función vacía
      }
      setIsYouTubeAPILoaded(false);
    };
  }, []);
  
  // Efecto para inicializar el reproductor cuando haya un video seleccionado y la API esté lista
  useEffect(() => {
    if (!youtubeVideoId || !isYouTubeAPILoaded || !selectedCampaign) {
      return;
    }

    console.log('Attempting to initialize YouTube player with video ID:', youtubeVideoId);
    
    // Verificar que la API de YouTube esté disponible
    if (!window.YT || !window.YT.Player) {
      console.error('YouTube API not loaded');
      // Reintentar después de un breve retraso
      const timer = setTimeout(() => {
        if (window.YT && window.YT.Player) {
          setIsYouTubeAPILoaded(true);
        }
      }, 1000);
      return () => clearTimeout(timer);
    }
    
    let player: any;
    let retryCount = 0;
    const maxRetries = 5;
    
    // Función para inicializar el reproductor
    const initializePlayer = () => {
      // Limpiar cualquier reproductor existente
      const playerElement = document.getElementById('youtube-player');
      if (!playerElement) {
        if (retryCount < maxRetries) {
          retryCount++;
          console.log(`YouTube player element not found, retrying (${retryCount}/${maxRetries})...`);
          setTimeout(initializePlayer, 500);
        } else {
          console.error('Max retries reached for YouTube player initialization');
        }
        return;
      }
      
      console.log('YouTube player element found, initializing player...');
      
      // Limpiar el contenedor
      playerElement.innerHTML = '';
      
      try {
        // Inicializar el nuevo reproductor
        player = new window.YT.Player('youtube-player', {
          height: '100%',
          width: '100%',
          videoId: youtubeVideoId,
          playerVars: {
            autoplay: 0,
            controls: 0, // Ocultamos los controles nativos
            disablekb: 1,
            rel: 0,
            modestbranding: 1,
            fs: 0,
            playsinline: 1,
            enablejsapi: 1,
            origin: window.location.origin
          },
          events: {
            onReady: (event: { target: YouTubePlayer }) => {
              console.log('YouTube player ready');
              const player = event.target;
              setYoutubePlayer(player);
              
              try {
                const duration = player.getDuration();
                setVideoDuration(duration);
                console.log('Video duration:', duration);
                
                // Configurar el estado inicial
                const playerState = player.getPlayerState();
                const isPlaying = playerState === 1; // 1 = reproduciendo
                setIsVideoPlaying(isPlaying);
                
                // Si el video ya está reproduciéndose, actualizar el progreso
                if (isPlaying) {
                  const time = player.getCurrentTime();
                  setCurrentTime(time);
                  
                  if (duration > 0) {
                    const progress = (time / duration) * 100;
                    setWatchProgress(Math.min(100, Math.max(0, progress)));
                  }
                }
                
                setIsYouTubePlayerReady(true);
                console.log('Player initialized. Playing:', isPlaying);
              } catch (e) {
                console.error('Error getting video duration:', e);
              }
            },
            onStateChange: (event: { data: number }) => {
              console.log('YouTube player state changed:', event.data);
              
              // Estados del reproductor de YouTube:
              // -1: no iniciado
              // 0: terminado
              // 1: reproduciendo
              // 2: pausado
              // 3: almacenando en búfer
              // 5: video seleccionado
              if (event.data === 1) {
                // Video en reproducción
                console.log('Video started playing');
                setIsVideoPlaying(true);
              } else if (event.data === 2) {
                // Video pausado
                console.log('Video paused');
                setIsVideoPlaying(false);
              } else if (event.data === 0) {
                // Video terminado
                console.log('Video ended');
                setIsVideoPlaying(false);
                setCurrentTime(videoDuration);
                setWatchProgress(100);
              }
            },
            onError: (event: { data: number }) => {
              console.error('YouTube player error:', event.data);
              setIsYouTubePlayerReady(false);
            }
          }
        });
      } catch (error) {
        console.error('Error initializing YouTube player:', error);
        setIsYouTubePlayerReady(false);
      }
    };
    
    // Iniciar la inicialización del reproductor
    initializePlayer();
    
    // Limpieza al desmontar
    return () => {
      console.log('Cleaning up YouTube player');
      try {
        if (player && typeof player.destroy === 'function') {
          player.destroy();
        }
        setIsYouTubePlayerReady(false);
        setIsVideoPlaying(false);
      } catch (e) {
        console.error('Error destroying YouTube player:', e);
      }
    };
    
  }, [youtubeVideoId, isYouTubeAPILoaded, selectedCampaign]);

  const handleWatchVideoClick = (campaign: Campaign) => {
    setSelectedCampaign(campaign);
    setWatchProgress(0);
    setCurrentTime(0);
    setIsWatching(true);
    setIsVideoPlaying(false);
  };

  const handleDialogClose = useCallback(async () => {
    // Primero actualizamos el estado para que la UI responda rápidamente
    setSelectedCampaign(null);
    setIsWatching(false);
    setWatchProgress(0);
    setCurrentTime(0);
    setIsVideoPlaying(false);
    
    // Luego manejamos la limpieza del reproductor
    if (youtubePlayer) {
      try {
        // Usamos una promesa para asegurarnos de que la limpieza se complete
        await new Promise<void>((resolve) => {
          try {
            // Verificamos si el reproductor aún tiene el método stopVideo
            if (youtubePlayer && typeof youtubePlayer.stopVideo === 'function') {
              youtubePlayer.stopVideo();
            }
            resolve();
          } catch (error) {
            // Ignoramos el error ya que el reproductor ya se está cerrando
            resolve();
          }
        });
      } catch (error) {
        // Ignoramos cualquier error en la limpieza
      } finally {
        // Aseguramos que el reproductor se limpie
        setYoutubePlayer(null);
      }
    } else {
      setYoutubePlayer(null);
    }
  }, [youtubePlayer]);

  const handleClaimReward = async () => {
    if (!selectedCampaign || !user) {
      console.error('No selected campaign or user');
      return;
    }

    // Verificar que el usuario haya visto suficiente tiempo del video
    if (watchProgress < 100) {
      toast({
        title: "Ver video completo",
        description: `Por favor mira el video completo para reclamar tus ${selectedCampaign.reward} Tubecoins.`,
        variant: "destructive",
      });
      return;
    }

    try {
      setIsLoading(true);
      
      // Simular una llamada a la API para reclamar la recompensa
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const rewardAmount = selectedCampaign.reward;
      const currentTubeCoins = typeof user.tubeCoins === 'number' ? user.tubeCoins : 0;
      const newTubeCoins = currentTubeCoins + rewardAmount;
      
      // Actualizar el perfil del usuario en Firebase
      await updateProfile(user.uid, { tubeCoins: newTubeCoins });
      
      // Actualizar la lista de campañas localmente
      const updatedCampaigns = campaigns.map(c =>
        c.id === selectedCampaign.id
          ? { 
              ...c, 
              remainingBudget: c.remainingBudget - c.reward, 
              views: c.views + 1,
              isActive: (c.remainingBudget - c.reward) > 0
            }
          : c
      );
      
      setCampaigns(updatedCampaigns.filter(c => c.isActive && c.remainingBudget > 0));
      
      // Mostrar notificación de éxito
      toast({
        title: "¡Recompensa reclamada!",
        description: `Has ganado ${rewardAmount} Tubecoins por ver "${selectedCampaign.title}".`,
        variant: "success",
      });
      
      // Cerrar el diálogo
      handleDialogClose();
    } catch (error) {
      console.error("Error al reclamar la recompensa:", error);
      toast({
        title: "Error",
        description: "No se pudo procesar la recompensa. Inténtalo de nuevo.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Las funciones auxiliares y las variables de YouTube ya están definidas al principio del componente

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[1,2,3].map(i => (
           <Card key={i} className="shadow-lg animate-pulse">
            <div className="aspect-video bg-muted rounded-t-lg" />
            <CardHeader>
              <div className="h-6 bg-muted rounded w-3/4 mb-2"></div>
              <div className="h-4 bg-muted rounded w-1/2"></div>
            </CardHeader>
            <CardContent>
              <div className="h-4 bg-muted rounded w-full mb-2"></div>
              <div className="h-4 bg-muted rounded w-2/3"></div>
            </CardContent>
            <CardFooter className="flex justify-between items-center">
              <div className="h-8 bg-muted rounded w-24"></div>
              <div className="h-10 bg-muted rounded w-28"></div>
            </CardFooter>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-8">
      <div className="text-center md:text-left">
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Presupuesto (Tubecoins)</label>
                <input
                  type="number"
                  placeholder="1000"
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                />
        </div>
      )}

      {/* This AlertDialog is now controlled by selectedCampaign state */}
      <AlertDialog open={!!selectedCampaign} onOpenChange={(isOpen) => { if(!isOpen) { handleDialogClose(); }}}>
        {selectedCampaign && ( // Ensure content is only rendered if a campaign is selected
          <AlertDialogContent className="max-w-3xl p-0 overflow-hidden">
            <div className="px-6 pt-6 pb-2">
              <AlertDialogHeader className="p-0">
                <AlertDialogTitle className="text-2xl">{selectedCampaign.title}</AlertDialogTitle>
                <AlertDialogDescription>
                  Watch this video for at least {REQUIRED_WATCH_SECONDS} seconds to earn {selectedCampaign.coinsPerView} TubeCoins.
                </AlertDialogDescription>
              </AlertDialogHeader>
            </div>
            <div className="aspect-video px-6">
              {youtubeVideoData ? (
                <div className="relative w-full aspect-video bg-black">
                  <div 
                    id="youtube-player" 
                    className="w-full h-full"
                    data-video-id={youtubeVideoId}
                  />
                  
                  {/* Overlay para controlar la reproducción */}
                  <div 
                    className={`absolute inset-0 transition-opacity duration-300 ${isVideoPlaying ? 'opacity-0 hover:opacity-100' : 'opacity-100'} bg-gray-900/70 flex items-center justify-center cursor-pointer`}
                    onClick={() => {
                      if (youtubePlayer) {
                        if (isVideoPlaying) {
                          youtubePlayer.pauseVideo();
                        } else {
                          youtubePlayer.playVideo();
                        }
                      }
                    }}
                    onContextMenu={(e) => e.preventDefault()}
                  >
                    {!isVideoPlaying ? (
                      <div className="bg-white/10 backdrop-blur-sm p-6 rounded-full border-2 border-white/20 hover:bg-white/20 transition-all duration-200 transform hover:scale-110">
                        <PlayCircle className="h-16 w-16 text-white" />
                      </div>
                    ) : (
                      <div className="absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity">
                        <div className="bg-white/10 backdrop-blur-sm p-6 rounded-full border-2 border-white/20">
                          <PauseIcon className="h-16 w-16 text-white" />
                        </div>
                      </div>
                    )}
                  </div>
                  
                  {/* Espacio reservado para mantener el espaciado */}
                  <div className="h-2"></div>
                </div>
              ) : (
                <p className="text-destructive-foreground">Invalid YouTube URL</p>
              )}
            </div>
            <div className="px-6 py-4">
              <div className="flex items-center gap-2 mb-3">
                <Clock className="h-5 w-5 text-primary" />
                <span className="text-sm font-medium">Watch Progress:</span>
              </div>
              <div className="w-full space-y-3">
                <div className="flex justify-between items-center text-sm">
                  <span className="text-muted-foreground">Progress: {Math.round(watchProgress)}%</span>
                  <span className="font-mono bg-muted px-2 py-1 rounded text-foreground">
                    {formatTime(currentTime)} / {formatTime(videoDuration)}
                  </span>
                </div>
                <Progress value={watchProgress} className="h-2" />
                {!isVideoPlaying && watchProgress > 0 && watchProgress < 100 && (
                  <p className="text-sm text-yellow-600 dark:text-yellow-400">
                    Video paused - play to continue earning
                  </p>
                )}
              </div>
              {watchProgress >= 100 && (
                <p className="text-sm text-green-600 font-semibold text-center mb-2">
                  You can now claim your reward!
                </p>
              )}
            </div>
            <div className="px-6 py-4 border-t border-border bg-background/95">
              <AlertDialogFooter className="p-0">
                <AlertDialogCancel onClick={handleDialogClose}>Close</AlertDialogCancel>
                <AlertDialogAction
                  onClick={handleClaimReward}
                  disabled={watchProgress < 100 || !isWatching}
                  className="bg-primary hover:bg-primary/90"
                >
                  <Coins className="mr-2 h-4 w-4"/> Claim {selectedCampaign.coinsPerView} TubeCoins
                </AlertDialogAction>
              </AlertDialogFooter>
            </div>
          </AlertDialogContent>
        )}
      </AlertDialog>
    </div>
  );
}
