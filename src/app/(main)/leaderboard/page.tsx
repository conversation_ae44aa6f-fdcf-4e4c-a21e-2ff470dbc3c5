
"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { BarChart3, Trophy, Users, Eye, TrendingUp } from "lucide-react";
import type { LeaderboardEntry } from "@/types";

// Mock data for leaderboards
const mockInfluencersLeaderboard: LeaderboardEntry[] = [
  { userId: "influencer1", displayName: "CryptoKing", photoURL: "https://placehold.co/40x40.png?text=CK", score: 150000 }, // Score = TubeCoins spent
  { userId: "influencer2", displayName: "TechGuru", photoURL: "https://placehold.co/40x40.png?text=TG", score: 120000 },
  { userId: "influencer3", displayName: "Fashionista", photoURL: "https://placehold.co/40x40.png?text=FS", score: 95000 },
  { userId: "influencer4", displayName: "GamerPro", photoURL: "https://placehold.co/40x40.png?text=GP", score: 80000 },
  { userId: "influencer5", displayName: "FoodieChannel", photoURL: "https://placehold.co/40x40.png?text=FC", score: 60000 },
];

const mockViewersLeaderboard: LeaderboardEntry[] = [
  { userId: "viewer1", displayName: "EagleEye", photoURL: "https://placehold.co/40x40.png?text=EE", score: 2500 }, // Score = TubeCoins earned
  { userId: "viewer2", displayName: "CoinCollector", photoURL: "https://placehold.co/40x40.png?text=CC", score: 2200 },
  { userId: "viewer3", displayName: "VideoFanatic", photoURL: "https://placehold.co/40x40.png?text=VF", score: 1900 },
  { userId: "viewer4", displayName: "AdWatcher", photoURL: "https://placehold.co/40x40.png?text=AW", score: 1500 },
  { userId: "viewer5", displayName: "PointsPro", photoURL: "https://placehold.co/40x40.png?text=PP", score: 1200 },
];

const getInitials = (name?: string | null) => {
    if (!name) return "U";
    const names = name.split(' ');
    if (names.length === 1) return names[0].charAt(0).toUpperCase();
    return names[0].charAt(0).toUpperCase() + names[names.length - 1].charAt(0).toUpperCase();
};

const LeaderboardTable = ({ data, title, scoreLabel }: { data: LeaderboardEntry[], title: string, scoreLabel: string }) => (
  <Card className="shadow-lg">
    <CardHeader>
      <CardTitle className="text-2xl font-semibold flex items-center gap-2">
        <Trophy className="h-6 w-6 text-yellow-500" /> {title}
      </CardTitle>
      <CardDescription>Top users based on {scoreLabel.toLowerCase()}.</CardDescription>
    </CardHeader>
    <CardContent>
      {data.length === 0 ? (
         <p className="text-muted-foreground text-center py-6">Leaderboard data is not available yet.</p>
      ) : (
      <ul className="space-y-3">
        {data.map((entry, index) => (
          <li
            key={entry.userId}
            className={`flex items-center justify-between p-3 rounded-lg transition-all
              ${index === 0 ? 'bg-gradient-to-r from-yellow-400 to-amber-500 text-white shadow-md' : 
                index === 1 ? 'bg-gradient-to-r from-slate-300 to-gray-400 text-gray-800 shadow-sm' :
                index === 2 ? 'bg-gradient-to-r from-orange-400 to-amber-600 text-white shadow-sm' : 
                'bg-card hover:bg-muted/50'}`}
          >
            <div className="flex items-center gap-3">
              <span className={`font-bold text-lg w-6 text-center ${index < 3 ? '' : 'text-muted-foreground'}`}>
                {index + 1}
              </span>
              <Avatar className="h-10 w-10 border-2 border-primary">
                <AvatarImage src={entry.photoURL} alt={entry.displayName} data-ai-hint="person avatar" />
                <AvatarFallback className={`${index < 3 ? 'bg-transparent' : 'bg-primary text-primary-foreground'}`}>
                    {getInitials(entry.displayName)}
                </AvatarFallback>
              </Avatar>
              <div>
                <p className={`font-semibold ${index < 3 ? '' : 'text-foreground'}`}>{entry.displayName}</p>
                <p className={`text-xs ${index < 3 ? 'opacity-80' : 'text-muted-foreground'}`}>User ID: ...{entry.userId.slice(-4)}</p>
              </div>
            </div>
            <div className={`font-bold text-lg ${index < 3 ? '' : 'text-primary'}`}>
              {entry.score.toLocaleString()} <span className="text-sm opacity-80">{scoreLabel === "TubeCoins Spent" ? "TC Spent" : "TC Earned"}</span>
            </div>
          </li>
        ))}
      </ul>
      )}
    </CardContent>
  </Card>
);

export default function LeaderboardPage() {
  const [influencers, setInfluencers] = useState<LeaderboardEntry[]>([]);
  const [viewers, setViewers] = useState<LeaderboardEntry[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulate fetching leaderboard data
    setLoading(true);
    setTimeout(() => {
      setInfluencers(mockInfluencersLeaderboard);
      setViewers(mockViewersLeaderboard);
      setLoading(false);
    }, 1000);
  }, []);

  if (loading) {
    return (
       <div className="container mx-auto py-8 px-4 md:px-6 space-y-6">
        {[1,2].map(i => (
           <Card key={i} className="shadow-lg animate-pulse">
            <CardHeader>
              <div className="h-8 bg-muted rounded w-1/2 mb-2"></div>
              <div className="h-4 bg-muted rounded w-3/4"></div>
            </CardHeader>
            <CardContent className="space-y-3">
              {[1,2,3,4,5].map(j => (
                <div key={j} className="flex items-center justify-between p-3 rounded-lg bg-muted">
                  <div className="flex items-center gap-3">
                    <div className="h-10 w-10 rounded-full bg-muted-foreground/20"></div>
                    <div className="space-y-1">
                      <div className="h-5 bg-muted-foreground/20 rounded w-32"></div>
                      <div className="h-3 bg-muted-foreground/20 rounded w-20"></div>
                    </div>
                  </div>
                  <div className="h-6 bg-muted-foreground/20 rounded w-24"></div>
                </div>
              ))}
            </CardContent>
          </Card>
        ))}
       </div>
    )
  }


  return (
    <div className="container mx-auto py-8 px-4 md:px-6">
      <div className="text-center md:text-left mb-8">
        <h1 className="text-3xl font-bold tracking-tight text-foreground flex items-center justify-center md:justify-start gap-2">
          <BarChart3 className="h-8 w-8 text-primary" /> Leaderboards
        </h1>
        <p className="text-muted-foreground">See who's topping the charts in ImpulsaTube Pro!</p>
      </div>

      <Tabs defaultValue="influencers" className="w-full">
        <TabsList className="grid w-full grid-cols-2 mb-6">
          <TabsTrigger value="influencers" className="py-2.5">
            <TrendingUp className="mr-2 h-5 w-5" /> Top Influencers
          </TabsTrigger>
          <TabsTrigger value="viewers" className="py-2.5">
            <Eye className="mr-2 h-5 w-5" /> Top Viewers
          </TabsTrigger>
        </TabsList>
        <TabsContent value="influencers">
          <LeaderboardTable data={influencers} title="Top Influencers" scoreLabel="TubeCoins Spent" />
        </TabsContent>
        <TabsContent value="viewers">
          <LeaderboardTable data={viewers} title="Top Viewers" scoreLabel="TubeCoins Earned" />
        </TabsContent>
      </Tabs>
    </div>
  );
}
