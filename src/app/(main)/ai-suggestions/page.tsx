
"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { Loader2, Lightbulb, Wand2 } from "lucide-react";
import { suggestVideoMetadata, type SuggestVideoMetadataInput, type SuggestVideoMetadataOutput } from '@/ai/flows/suggest-video-metadata';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";

const suggestionsSchema = z.object({
  videoContentDescription: z.string().min(10, "Description must be at least 10 characters."),
  targetAudience: z.string().min(3, "Target audience must be at least 3 characters."),
  keywords: z.string().min(3, "Keywords must be at least 3 characters (comma separated)."),
});

type SuggestionsFormData = z.infer<typeof suggestionsSchema>;

export default function AiSuggestionsPage() {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [suggestedData, setSuggestedData] = useState<SuggestVideoMetadataOutput | null>(null);

  const form = useForm<SuggestionsFormData>({
    resolver: zodResolver(suggestionsSchema),
    defaultValues: {
      videoContentDescription: "",
      targetAudience: "",
      keywords: "",
    },
  });

  const onSubmit = async (data: SuggestionsFormData) => {
    setIsLoading(true);
    setSuggestedData(null);
    try {
      const input: SuggestVideoMetadataInput = {
        videoContentDescription: data.videoContentDescription,
        targetAudience: data.targetAudience,
        keywords: data.keywords,
      };
      const result = await suggestVideoMetadata(input);
      setSuggestedData(result);
      toast({
        title: "Suggestions Generated!",
        description: "AI has provided suggestions for your video.",
      });
    } catch (error) {
      console.error("AI Suggestion Error:", error);
      toast({
        title: "Error Generating Suggestions",
        description: "Something went wrong. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-8 px-4 md:px-6 max-w-3xl">
      <Card className="shadow-xl">
        <CardHeader>
          <CardTitle className="text-3xl font-bold flex items-center gap-2">
            <Lightbulb className="h-8 w-8 text-primary" /> AI Content Helper
          </CardTitle>
          <CardDescription>
            Get AI-powered suggestions for your video descriptions and tags to improve discoverability.
          </CardDescription>
        </CardHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <CardContent className="space-y-6">
              <FormField
                control={form.control}
                name="videoContentDescription"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-base">Video Content Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="e.g., A detailed tutorial on how to bake a chocolate cake from scratch, covering ingredients, mixing techniques, and baking tips."
                        className="min-h-[120px]"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Provide a clear and concise description of what your video is about.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="targetAudience"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-base">Target Audience</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., Beginner bakers, cooking enthusiasts, families" {...field} />
                    </FormControl>
                     <FormDescription>
                      Who is this video for? This helps tailor suggestions.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="keywords"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-base">Existing Keywords (comma-separated)</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., chocolate cake, baking, tutorial, recipe" {...field} />
                    </FormControl>
                    <FormDescription>
                      Any specific keywords you already have in mind.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
            <CardFooter className="border-t pt-6">
              <Button type="submit" disabled={isLoading} className="w-full md:w-auto">
                {isLoading ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Wand2 className="mr-2 h-4 w-4" />
                )}
                Generate Suggestions
              </Button>
            </CardFooter>
          </form>
        </Form>
      </Card>

      {suggestedData && (
        <Card className="mt-8 shadow-xl">
          <CardHeader>
            <CardTitle className="text-2xl font-bold">AI Suggestions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <h3 className="text-xl font-semibold mb-2">Suggested Description:</h3>
              <Textarea
                readOnly
                value={suggestedData.suggestedDescription}
                className="min-h-[150px] bg-muted/50"
              />
            </div>
            <div>
              <h3 className="text-xl font-semibold mb-2">Suggested Tags:</h3>
              <div className="flex flex-wrap gap-2">
                {suggestedData.suggestedTags.map((tag, index) => (
                  <Badge key={index} variant="secondary" className="text-sm px-3 py-1">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          </CardContent>
           <CardFooter>
            <Button onClick={() => navigator.clipboard.writeText(`Description:
${suggestedData.suggestedDescription}

Tags:
${suggestedData.suggestedTags.join(', ')}`)}>
              Copy Suggestions
            </Button>
          </CardFooter>
        </Card>
      )}
    </div>
  );
}
